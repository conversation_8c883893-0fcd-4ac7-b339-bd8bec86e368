// Placeholder schema file for drizzle-kit compatibility
// This project uses traditional SQL migrations in server/migrations/
// This file exists to satisfy drizzle.config.ts requirements

import { pgTable, serial, varchar, text, integer, boolean, timestamp, decimal } from 'drizzle-orm/pg-core';

// Users table schema (matches server/migrations/001_create_users_table.sql)
export const users = pgTable('users', {
  id: serial('id').primaryKey(),
  email: varchar('email', { length: 255 }).notNull().unique(),
  passwordHash: varchar('password_hash', { length: 255 }).notNull(),
  firstName: varchar('first_name', { length: 100 }).notNull(),
  lastName: varchar('last_name', { length: 100 }).notNull(),
  notificationEnabled: boolean('notification_enabled').default(true),
  preferredLanguage: varchar('preferred_language', { length: 10 }).default('en'),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// Monitored destinations table schema (matches server/migrations/002_create_monitored_destinations_table.sql)
export const monitoredDestinations = pgTable('monitored_destinations', {
  id: serial('id').primaryKey(),
  location: text('location').notNull(),
  riskLevel: integer('risk_level'),
  lastChecked: timestamp('last_checked').defaultNow(),
  userId: integer('user_id').references(() => users.id),
  latitude: decimal('latitude', { precision: 10, scale: 8 }),
  longitude: decimal('longitude', { precision: 11, scale: 8 }),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// Alerts table schema (matches server/migrations/003_create_alerts_table.sql)
export const alerts = pgTable('alerts', {
  id: serial('id').primaryKey(),
  userId: integer('user_id').references(() => users.id),
  title: varchar('title', { length: 255 }).notNull(),
  message: text('message'),
  status: varchar('status', { length: 20 }).default('unread'),
  priority: varchar('priority', { length: 10 }).default('medium'),
  createdAt: timestamp('created_at').defaultNow(),
  readAt: timestamp('read_at'),
});
